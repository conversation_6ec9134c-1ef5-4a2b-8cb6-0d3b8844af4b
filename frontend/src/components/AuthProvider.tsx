'use client';

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import { createClient } from '@/lib/supabase/client';
import { User, Session } from '@supabase/supabase-js';
import posthog from 'posthog-js';
import { SupabaseClient } from '@supabase/supabase-js';

type AuthContextType = {
  supabase: SupabaseClient;
  session: Session | null;
  user: User | null;
  isLoading: boolean;
  signOut: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const supabase = createClient();
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const sendWelcomeEmailForNewUser = async (user: User) => {
    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
      await fetch(`${backendUrl}/api/send-welcome-email-background`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: user.email,
          name: user.user_metadata?.full_name || user.user_metadata?.name,
        }),
      });
    } catch (error) {
      console.error('Welcome email failed:', error);
    }
  };

  useEffect(() => {
    const getInitialSession = async () => {
      const {
        data: { session: currentSession },
      } = await supabase.auth.getSession();
      setSession(currentSession);
      setUser(currentSession?.user ?? null);
      setIsLoading(false);
    };

    getInitialSession();

    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, newSession) => {
        const previousUser = user;
        setSession(newSession);
        setUser(newSession?.user ?? null);
        
        // Handle new user signup (both OAuth and email)
        if (event === 'SIGNED_IN' && newSession?.user && !previousUser) {
          const userCreatedAt = new Date(newSession.user.created_at);
          const timeDiff = Date.now() - userCreatedAt.getTime();

          // If user created within last hour, it's a new signup
          if (timeDiff < 3600000) { // 1 hour in milliseconds
            // Track signup event in PostHog
            posthog.capture('user_signed_up', {
              user_id: newSession.user.id,
              email: newSession.user.email,
              signup_method: newSession.user.app_metadata?.provider || 'email',
              created_at: newSession.user.created_at,
              is_first_time_signup: true
            });

            // Identify user in PostHog for future tracking
            posthog.identify(newSession.user.id, {
              email: newSession.user.email,
              signup_date: newSession.user.created_at,
              signup_method: newSession.user.app_metadata?.provider || 'email'
            });

            // Send welcome email
            await sendWelcomeEmailForNewUser(newSession.user);
          }
        }
        
        // No need to set loading state here as initial load is done
        // and subsequent changes shouldn't show a loading state for the whole app
        if (isLoading) setIsLoading(false);
      },
    );

    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, [supabase]); // Removed isLoading from dependencies to prevent recreation

  const signOut = async () => {
    await supabase.auth.signOut();
    // State updates will be handled by onAuthStateChange
  };

  const value = {
    supabase,
    session,
    user,
    isLoading,
    signOut,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
