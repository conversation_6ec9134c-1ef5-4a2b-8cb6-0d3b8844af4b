# Customerly Live Chat Integration

This directory contains the Customerly live chat integration for Atlas dashboard.

## Overview

Customerly provides customer support chat functionality that appears as a floating widget on the main dashboard page. The integration includes user authentication, custom attributes, and analytics callbacks.

## Components

### `customerly-chat.tsx`
- **Purpose**: Main integration component that initializes and configures the Customerly chat widget
- **Location**: Appears on all dashboard pages (`/dashboard/*`)
- **Features**:
  - User authentication with Supabase user data
  - Custom user attributes (signup date, role, last sign-in)
  - Analytics callbacks for lead generation and conversation tracking
  - Responsive design (desktop and mobile)

### `chat-controls.tsx`
- **Purpose**: Optional manual control buttons for testing and development
- **Usage**: Not used in production, available for debugging
- **Functions**: Open, close, show, hide chat widget

## Configuration

### Environment Variables
```bash
# Required: Your Customerly Project ID
NEXT_PUBLIC_CUSTOMERLY_PROJECT_ID=your_project_id_here
```

### Provider Setup
The `CustomerlyProvider` is configured in `src/app/providers.tsx` and wraps the entire application.

## User Data Integration

The chat automatically integrates with authenticated users:
- **User ID**: Supabase user ID
- **Email**: User's email address
- **Name**: Full name from user metadata or email fallback
- **Custom Attributes**:
  - `signup_date`: Unix timestamp of account creation
  - `user_role`: User role from metadata
  - `last_sign_in`: Unix timestamp of last sign-in

## Analytics Integration

The component includes callback hooks for analytics tracking:
- `onLeadGenerated`: When a new conversation starts (potential lead)
- `onNewConversation`: When any new conversation begins
- `onChatOpened`: When the chat widget is opened

**TODO**: Integrate these callbacks with your analytics platform (PostHog, etc.)

## Styling

The chat widget uses Customerly's hosted UI with custom theming:
- **Accent Color**: `#3b82f6` (blue to match Atlas theme)
- **Contrast Color**: `#ffffff` (white)
- **Position**: Bottom-right corner (default)
- **Mobile**: Fully responsive and mobile-optimized

## Development

### Testing
Use the `ChatControls` component for manual testing:
```tsx
import { ChatControls } from '@/components/customerly/chat-controls';

// Add to any component for testing
<ChatControls className="fixed top-4 right-4 z-50" />
```

### Debugging
Enable console logging by adding debug statements to the useEffect hooks in `customerly-chat.tsx`.

## Production Deployment

1. **Set Environment Variable**: Ensure `NEXT_PUBLIC_CUSTOMERLY_PROJECT_ID` is set in production
2. **Analytics Integration**: Connect the callback functions to your analytics platform
3. **Testing**: Verify chat widget appears on dashboard and user data is properly passed

## Support

- **Customerly Documentation**: https://docs.customerly.io
- **React Library**: https://www.npmjs.com/package/react-live-chat-customerly
- **Atlas Integration**: Contact development team for customization
